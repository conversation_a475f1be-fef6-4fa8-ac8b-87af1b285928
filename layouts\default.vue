<template>
  <div class="container">
    <header>
    <nav class="navbar">
      <NuxtLink to="/" class="nav-link" active-class="router-link-active">Home</NuxtLink>
      <span class="separator">|</span>
      <NuxtLink to="/about" class="nav-link" active-class="router-link-active">About</NuxtLink>
      <span class="separator">|</span>
      <NuxtLink to="/products" class="nav-link" active-class="router-link-active">Products</NuxtLink>
    </nav>
    </header>
    <main class="page-content">
      <NuxtPage />
    </main>
  </div>
  <footer class="footer">
    <p>&copy; {{ new Date().getFullYear() }} MyApp. All rights reserved.</p>
    <p class="footer-note">Building dreams with every line of code.</p>
  </footer>
</template>

<script setup>
</script>

<style scoped>
.container {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #2c3e50;
}

.navbar {
  background-color: #3498db;
  padding: 10px 20px;
  border-radius: 8px;
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  font-weight: 600;
}

.nav-link {
  color: white;
  text-decoration: none;
  margin: 0 10px;
  transition: color 0.3s ease;
}

.nav-link.router-link-active {
  color: #f39c12;
  font-weight: 700;
  text-decoration: underline;
}

.nav-link:hover {
  color: #1d6fa5;
  text-decoration: underline;
}

.separator {
  color: white;
  margin: 0 5px;
}

.page-content {
  background-color: #f9f9f9;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  min-height: 400px;
}

.footer {
  background-color: #222;
  color: #ccc;
  padding: 20px;
  text-align: center;
  margin-top: 30px;
  font-family: Arial, sans-serif;
  font-size: 14px;
}

.footer-note {
  font-size: 12px;
  color: #aaa;
  margin-top: 5px;
}
</style>



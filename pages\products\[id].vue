<template>
  <div>
    <p>product details for {{ id }}</p>
    <p>
      Building dreams with every line of code. <br />
      Where logic meets creativity in perfect harmony.
    </p>
  </div>
</template>

<script setup>
const { id } = useRoute().params;
definePageMeta({
    layout: "products"
})
</script>

<style scoped>
div {
  max-width: 600px;
  margin: 40px auto;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #34495e;
}</style>

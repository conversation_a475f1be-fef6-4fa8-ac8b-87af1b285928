<template>
    <div class="card">
        <div class="card-image">
            <img :src="product.image" alt="Product_image" class="h-30 w-18 mx-auto mb-3 object-contain" />
        </div>
        <div class="card-content">
            <p class="font-bold text-gray-700 text-sm truncate hover:text-blue-600
             transition-colors duration-200">{{ product.title }}</p>
            <p class="text-gray-600 text-sm">{{ product.description }}</p>
            <p class="text-gray-600 text-sm">ID:{{ product.id }}</p>
            <p class="text-gray-600 text-sm">Price:{{ product.price }}</p>
            <p class="text-gray-600 text-sm">Category:{{ product.category }}</p>
        </div>

        
    </div>
</template>

<script setup>

const {product}= defineprops(['product'])
</script>

<style scoped>

</style>